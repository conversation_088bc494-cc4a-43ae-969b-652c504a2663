function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}function ownKeys(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,r)}return o}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(o),!0).forEach((function(t){_defineProperty(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):ownKeys(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function _defineProperty(e,t,o){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var KLAVIYO_JS_REGEX=/(\/onsite\/js\/([a-zA-Z]{6})\/klaviyo\.js\?company_id=([a-zA-Z0-9]{6}).*|\/onsite\/js\/klaviyo\.js\?company_id=([a-zA-Z0-9]{6}).*)/;function logFailedKlaviyoJsLoad(e,t,o){var r={metric_group:"onsite",events:[{metric:"klaviyoJsCompanyIdMisMatch",log_to_statsd:!0,log_to_s3:!0,log_to_metrics_service:!1,event_details:{script:e,templated_company_id:t,fastly_forwarded:o,hostname:window.location.hostname}}]};fetch("https://a.klaviyo.com/onsite/track-analytics?company_id=".concat(t),{headers:{accept:"application/json","content-type":"application/json"},referrerPolicy:"strict-origin-when-cross-origin",body:JSON.stringify(r),method:"POST",mode:"cors",credentials:"omit"})}!function(e){var t="RW4muz",o=JSON.parse("[]"),r="true"==="True".toLowerCase(),n=JSON.parse("[]");if(!(document.currentScript&&document.currentScript instanceof HTMLScriptElement&&document.currentScript.src&&document.currentScript.src.match(KLAVIYO_JS_REGEX))||null!==(e=document.currentScript.src)&&void 0!==e&&e.includes(t)){var i=window.klaviyoModulesObject;if(/musical_ly|bytedance/i.test(navigator.userAgent)&&(window.tikTokEvent={company_id:t,kl_key:window.__klKey,fastly_forwarded:r},i)){var a=i,c=a.companyId,s=a.serverSideRendered;window.tikTokEvent=_objectSpread(_objectSpread({},window.tikTokEvent),{},{window_company_id:c,server_side_rendered:s})}var d=new URL(window.location.href);if(d.searchParams.has("crawler")&&"tiktok_preloading"===d.searchParams.get("crawler")&&(window.tikTokCrawler={company_id:t,kl_key:window.__klKey}),window.klKeyCollision=window.__klKey&&window.__klKey!==t?{companyId:t,klKey:window.__klKey}:void 0,window._learnq=window._learnq||[],window.__klKey=window.__klKey||t,i||(window._learnq.push(["account",t]),i={companyId:t,loadTime:new Date,loadedModules:{},loadedCss:{},serverSideRendered:!0,assetSource:"",v2Route:r,extendedIdIdentifiers:o,env:"web",featureFlags:n},Object.defineProperty(window,"klaviyoModulesObject",{value:i,enumerable:!1})),t===i.companyId&&i.serverSideRendered){var l,y,p,u={},f=document,w=f.head,_=JSON.parse("noModule"in f.createElement("script")||function(){try{return new Function('import("")'),!0}catch(e){return!1}}()?"{\u0022static\u0022: {\u0022js\u0022: [\u0022https://static\u002Dtracking.klaviyo.com/onsite/js/fender_analytics.c61482660e7765378c94.js?cb\u003D1&v2-route\u003D1\u0022, \u0022https://static\u002Dtracking.klaviyo.com/onsite/js/static.b75b0459cacc803751b4.js?cb\u003D1&v2-route\u003D1\u0022, \u0022https://static.klaviyo.com/onsite/js/runtime.1d4265c6c34d28ce0d1e.js?cb\u003D1&v2-route\u003D1\u0022, \u0022https://static.klaviyo.com/onsite/js/sharedUtils.2020e3089e11d3ccd617.js?cb\u003D1&v2-route\u003D1\u0022]}}":"{\u0022static\u0022: {\u0022js\u0022: [\u0022https://static\u002Dtracking.klaviyo.com/onsite/js/fender_analytics.c7527d41ffee2aec8341.js?cb\u003D1&v2-route\u003D1\u0022, \u0022https://static\u002Dtracking.klaviyo.com/onsite/js/static.b7d2174aa7bd85d119f2.js?cb\u003D1&v2-route\u003D1\u0022, \u0022https://static.klaviyo.com/onsite/js/runtime.f9adb62cdf45bee2f9cf.js?cb\u003D1&v2-route\u003D1\u0022, \u0022https://static.klaviyo.com/onsite/js/sharedUtils.5f0dba16e0c9ebb41a69.js?cb\u003D1&v2-route\u003D1\u0022]}}"),m=i,v=m.loadedCss,b=m.loadedModules;for(l in _)if(_.hasOwnProperty(l)){var S=_[l];S.js.forEach((function(e){var t=e.split("?")[0];t&&!b[t]&&(k(e),b[t]=(new Date).toISOString())}));var g=S.css;g&&!v[g]&&(y=g,p=void 0,(p=f.createElement("link")).rel="stylesheet",p.href=y,w.appendChild(p),v[g]=(new Date).toISOString())}}else console.warn("Already loaded for account ".concat(i.companyId,". Skipping account ").concat(t,"."))}else{console.warn("Not loading ".concat(document.currentScript.src," for ").concat(t));try{logFailedKlaviyoJsLoad(document.currentScript.src,t,r)}catch(e){console.warn("Error logging klaviyo.js company mismatch")}}function k(e){if(!u[e]){var t=f.createElement("script");t.type="text/javascript",t.async=!0,t.src=e,t.crossOrigin="anonymous",w.appendChild(t),u[e]=!0}}}();
