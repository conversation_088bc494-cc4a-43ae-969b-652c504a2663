(()=>{function ready(fn){if(document.readyState!=="loading"){fn()}else if(document.addEventListener){document.addEventListener("DOMContentLoaded",fn)}else{document.attachEvent("onreadystatechange",()=>{if(document.readyState!=="loading"){fn()}})}}function featureNotFound(feature){return new Error(`The feature { name: "${feature.name}", version: "${feature.version}"} does not exist`)}function couldNotCreateEntry(scriptId){return new Error(`Could not create registry entry ${scriptId}`)}function couldNotAddToQuerySelectors(){return new Error("Cannot register a feature with the same selector twice")}function invalidFeaturesArray(features){return new Error(`Features should be an Array. Received: ${JSON.stringify(features)}`)}function invalidFeature(feature){return new Error(`Features should be defined as \`{ name: "name", version: "version" }\`. Received: ${JSON.stringify(feature)}`)}function alreadyLoaded(scriptId,versionLoaded){return new Error(`${scriptId} has already been loaded at version ${versionLoaded}`)}var featuresJSON;function getFeaturesJSON(){if(featuresJSON){return featuresJSON}const features=document.getElementById("shopify-features");if(features){try{featuresJSON=JSON.parse(features.textContent)}catch(_error){}}else{featuresJSON={}}return featuresJSON}function getBetas(){const features=getFeaturesJSON();if(features){try{return features.betas.reduce((lookup2,currentFlag)=>{lookup2[currentFlag]=true;return lookup2},{})}catch(_error){}}return{}}function getLocale(){const features=getFeaturesJSON();return features.locale||"en"}function noop(){}function createAutoLoadLookup(){const lookup2={};function add(selector,newFeature){lookup2[selector]=lookup2[selector]||[];const featuresForSelector=lookup2[selector];for(let i=0;i<featuresForSelector.length;i++){const{name,version}=featuresForSelector[i];const alreadyHasFeature=newFeature.name===name;if(alreadyHasFeature){if(newFeature.version!==version){throw couldNotAddToQuerySelectors(selector)}return}}featuresForSelector.push(newFeature)}function getFeatures(){return Object.keys(lookup2).reduce((features,selector)=>{const element=document.querySelector(selector);if(!element){return features}const featuresForElement=lookup2[selector];delete lookup2[selector];return features.concat(featuresForElement)},[])}return{add,getFeatures}}var betas=null;function isInBeta(betaFlag){if(betas){return betas[betaFlag]}betas=getBetas();return isInBeta(betaFlag)}var scriptsLoaded=[];var scriptsFailed=[];function isScriptLoaded(script){return scriptsLoaded.indexOf(script)>-1}function isScriptFailed(script){return scriptsFailed.indexOf(script)>-1}function createScriptTag(src,props,cb){let script=document.querySelector(`script[src="${src}"]`);function onLoad(){scriptsLoaded.push(script);removeEvents();cb(null,script)}function onError(){scriptsFailed.push(script);removeEvents();cb(new Error(`load error: ${src}`))}function addEvents(){script.addEventListener("load",onLoad);script.addEventListener("error",onError)}function removeEvents(){script.removeEventListener("load",onLoad);script.removeEventListener("error",onError)}if(script&&isScriptLoaded(script)){onLoad();return}if(script&&isScriptFailed(script)){onError();return}if(script){addEvents();return}script=document.createElement("script");Object.keys(props).forEach(key=>{script.setAttribute(key,props[key])});if(script.getAttribute("defer")===null){script.setAttribute("defer","")}script.src=src;script.crossorigin="anonymous";addEvents();document.head.appendChild(script)}function getEntriesAndFeatures(features,currentErrors,errorIfBetaNotOk){return features.reduce((entriesAndFeatures,feature)=>{const onLoad=feature.onLoad||noop;try{const entry=getEntry(feature);const betaFlag=entry.betaFlag;const betaOk=!betaFlag||isInBeta(betaFlag);if(errorIfBetaNotOk&&!betaOk){throw featureNotFound(feature)}if(betaOk){entriesAndFeatures.push([entry,feature])}}catch(error){onLoad(error);currentErrors.push(error)}return entriesAndFeatures},[])}function loadScripts(entriesAndFeatures,currentErrors,callback){let totalSourcesToLoad=entriesAndFeatures.reduce((acc,[entry])=>{return acc+(entry.srcs?entry.srcs.length:1)},0);if(totalSourcesToLoad===0){callback(currentErrors);return}entriesAndFeatures.forEach(([entry,feature])=>{const onLoad=feature.onLoad||noop;const sources=entry.srcs||[entry.src];let featureSourcesToLoad=sources.length;const featureErrors=[];sources.forEach(src=>{createScriptTag(src,entry.props,error=>{if(error){currentErrors.push(error);featureErrors.push(error)}featureSourcesToLoad--;if(featureSourcesToLoad===0){if(featureErrors.length===0){onLoad(null)}else if(featureErrors.length===1){onLoad(featureErrors[0])}else{onLoad(featureErrors)}}totalSourcesToLoad--;if(totalSourcesToLoad===0){callback(currentErrors)}})})})}function loadMultiple(features,errorIfNotInBeta,callback){const currentErrors=[];const entriesAndFeatures=getEntriesAndFeatures(features,currentErrors,errorIfNotInBeta);loadScripts(entriesAndFeatures,currentErrors,scriptLoadErrors=>{const errors=scriptLoadErrors.length===0?null:scriptLoadErrors;callback(errors)})}function loadMultipleErrorIfNotInBeta(features,callback){loadMultiple(features,true,callback)}function loadMultipleSilentIfNotInBeta(features,callback){loadMultiple(features,false,callback)}var lookup=createAutoLoadLookup();function autoloadFeatures(callback){const cb=callback||noop;loadMultipleSilentIfNotInBeta(lookup.getFeatures(),cb)}function urlForFeature({name,version,legacy,baseName=null,locale=null,localesSupported=[],fileName=null}){const cloudFolderName=baseName||name;let scriptName=fileName||cloudFolderName;if(scriptName.endsWith(".js")){scriptName=scriptName.slice(0,-3)}if(legacy){scriptName=`${scriptName}-legacy`}if(locale){locale=localesSupported.length===0||localesSupported.includes(locale)?locale:"en";scriptName=`${scriptName}.${locale}`}if((name==="shop-js"||name.startsWith("shop-js/"))&&window.Shopify.developmentShopJsUrl){return`https://${window.Shopify.developmentShopJsUrl}/${scriptName}.js`}const cdnHost=window.Shopify&&window.Shopify.cdnHost||"cdn.shopify.com";const urlTokens=[cdnHost,"shopifycloud",cloudFolderName];if(version!==void 0){urlTokens.push(`v${version}`)}urlTokens.push(`${scriptName}.js`);return`https://${urlTokens.join("/")}`}var registry={};var scriptsConsumed={};function addToRegistry({name,baseName,version,betaFlag,fileName,fileNames,legacy,localized,localesSupported,autoLoadSelector,props={}}){const scriptId=`${name}@${version||"latest"}`;if(registry[scriptId]){throw couldNotCreateEntry(scriptId)}if(autoLoadSelector){const autoLoadSelectorArray=Array.isArray(autoLoadSelector)?autoLoadSelector:[autoLoadSelector];autoLoadSelectorArray.forEach(selector=>{lookup.add(selector,{name,version})})}registry[scriptId]={props,betaFlag,scriptId,name,baseName,version,locale:getLocale(),localized,localesSupported,legacy,fileName,fileNames}}function register(entry){if(window.Shopify.modules){entry.legacy=false;entry.props={type:"module"};addToRegistry(entry)}else if(entry.hasLegacy){entry.legacy=true;entry.props={nomodule:""};addToRegistry(entry)}}function getEntry(feature){const scriptId=`${feature.name}@${feature.version||"latest"}`;const entry=registry[scriptId];if(!entry){throw featureNotFound(feature)}const name=entry.name;const baseName=entry.baseName;const version=entry.version;const scriptLocale=entry.localized&&entry.locale;const legacy=entry.legacy;const localesSupported=entry.localesSupported;if(scriptsConsumed[name]&&scriptsConsumed[name]!==version){throw alreadyLoaded(scriptId,scriptsConsumed[name])}scriptsConsumed[name]=version;const sources=[];(entry.fileNames||[entry.fileName]).forEach(fileName=>{sources.push(urlForFeature({name,baseName,version,legacy,locale:scriptLocale,localesSupported,fileName}))});if(sources.length===1){entry.src=sources[0]}else if(sources.length>1){entry.srcs=sources}return entry}function validateFeature(feature){if(feature&&typeof feature.name==="string"&&typeof feature.version==="string"){return}throw invalidFeature(feature)}function loadFeatures(features,callback){const cb=callback||noop;if(Array.isArray(features)){features.forEach(validateFeature);loadMultipleErrorIfNotInBeta(features,cb);return}throw invalidFeaturesArray(features)}function createElementObserver(callbackDomElementAdded){let observer=null;return{get isObserving(){return Boolean(observer)},enable(){if(this.isObserving){return}observer=new MutationObserver(mutationList=>{let hasNewNodes=false;for(let i=0;i<mutationList.length;i++){if(mutationList[i].addedNodes.length){hasNewNodes=true;break}}if(hasNewNodes){callbackDomElementAdded()}});observer.observe(document.body,{childList:true,subtree:true})},disable(){if(!this.isObserving){return}observer.disconnect();observer=null}}}function handleCallQueue(functionName,replacingFunction){const currentQueue=window.Shopify[functionName]&&window.Shopify[functionName].q;if(currentQueue&&Array.isArray(currentQueue)){currentQueue.forEach(args=>{replacingFunction(...args)})}window.Shopify[functionName]=replacingFunction}window.Shopify=window.Shopify||{};window.Shopify.featureAssets=window.Shopify.featureAssets||{};window.Shopify.featureAssets["shop-js"]=window.Shopify.featureAssets["shop-js"]||{};register({name:"model-viewer",version:"0.6",hasLegacy:true,autoLoadSelector:'model-viewer[data-shopify-feature="0.6"]'});register({name:"model-viewer",version:"0.7",hasLegacy:true,autoLoadSelector:'model-viewer[data-shopify-feature="0.7"]'});register({name:"model-viewer",version:"0.8",hasLegacy:true,autoLoadSelector:'model-viewer[data-shopify-feature="0.8"]'});register({name:"model-viewer",version:"1.2",hasLegacy:true,autoLoadSelector:'model-viewer[data-shopify-feature="1.2"]'});register({name:"model-viewer",version:"1.7",hasLegacy:true,autoLoadSelector:'model-viewer[data-shopify-feature="1.7"]'});register({name:"model-viewer",version:"1.9",hasLegacy:true,autoLoadSelector:'model-viewer[data-shopify-feature="1.9"]'});register({name:"model-viewer",version:"1.10",hasLegacy:true,autoLoadSelector:'model-viewer[data-shopify-feature="1.10"]'});register({name:"model-viewer",version:"1.11",hasLegacy:true,autoLoadSelector:'model-viewer[data-shopify-feature="1.11"]'});register({name:"model-viewer",version:"1.12",hasLegacy:true,autoLoadSelector:'model-viewer[data-shopify-feature="1.12"]'});register({name:"shop-js/shopify-payment-terms",baseName:"shop-js",hasLegacy:false,localized:false,fileNames:Shopify.featureAssets["shop-js"]["shopify-payment-terms"]||Shopify.featureAssets["shop-js"]["payment-terms"]||["client"],autoLoadSelector:["shopify-payment-terms"]});register({name:"shop-js/shop-login-button",baseName:"shop-js",hasLegacy:false,localized:false,fileNames:Shopify.featureAssets["shop-js"]["login-button"]||Shopify.featureAssets["shop-js"]["shop-login-button"]||["client"],autoLoadSelector:["shop-login-button"]});register({name:"shop-js/shop-pay-checkout-sheet",baseName:"shop-js",hasLegacy:false,localized:false,fileNames:Shopify.featureAssets["shop-js"]["shop-pay-checkout-sheet"]||["client"],autoLoadSelector:["shop-pay-checkout-sheet"]});register({name:"shop-js/shop-toast-manager",baseName:"shop-js",hasLegacy:false,localized:false,fileNames:Shopify.featureAssets["shop-js"]["shop-toast-manager"]||["client"],autoLoadSelector:["shop-toast-manager"]});register({name:"shop-js/avatar",baseName:"shop-js",hasLegacy:false,localized:false,fileNames:Shopify.featureAssets["shop-js"].avatar||["client"],autoLoadSelector:["shop-user-avatar"]});register({name:"model-viewer-ui",version:"1.0",hasLegacy:true,localized:true,localesSupported:["bg-BG","cs","da","de","el","es","fi","fr","hi","hr-HR","hu","id","it","ja","ko","lt-LT","ms","nb","nl","pl","pt-BR","pt-PT","ro-RO","ru","sk-SK","sl-SI","sv","th","tr","vi","zh-CN","zh-TW"]});register({name:"shopify-xr",version:"1.0",baseName:"shopify-xr-js",fileName:"shopify-xr",localized:true,localesSupported:["bg-BG","cs","da","de","el","es","fi","fr","hi","hr-HR","hu","id","it","ja","ko","lt-LT","ms","nb","nl","pl","pt-BR","pt-PT","ro-RO","ru","sk-SK","sl-SI","sv","th","tr","vi","zh-CN","zh-TW"]});register({name:"video-ui",baseName:"shopify-plyr",version:"1.0",hasLegacy:true,localized:true,localesSupported:["cs","da","de","es","fi","fr","hi","it","ja","ko","ms","nb","nl","pl","pt-BR","pt-PT","sv","th","tr","zh-CN","zh-TW"]});register({name:"video-ui",baseName:"shopify-plyr",version:"1.1",hasLegacy:true,localized:true,localesSupported:["cs","da","de","es","fi","fr","hi","it","ja","ko","ms","nb","nl","pl","pt-BR","pt-PT","sv","th","tr","zh-CN","zh-TW"]});register({name:"video-ui",baseName:"plyr",version:"2.0",hasLegacy:true,localized:true,localesSupported:["bg-BG","cs","da","de","el","es","fi","fr","hi","hr-HR","hu","id","it","ja","ko","lt-LT","ms","nb","nl","pl","pt-BR","pt-PT","ro-RO","ru","sk-SK","sl-SI","sv","th","tr","vi","zh-CN","zh-TW"],fileName:"shopify-plyr"});register({name:"media-analytics",version:"0.1",hasLegacy:true,fileName:"analytics",betaFlag:"rich-media-storefront-analytics",autoLoadSelector:["video","model-viewer",'a[rel="ar"]','a[href*="package=com.google.ar.core;action=android.intent.action.VIEW;"]',"[data-shopify-xr]",'iframe[src^="https://www.youtube.com/embed/"]','iframe[src^="https://player.vimeo.com/video/"]']});register({name:"consent-tracking-api",version:"0.1",hasLegacy:true});register({name:"consent-tracking-api",version:"0.2",hasLegacy:true});ready(()=>{handleCallQueue("loadFeatures",loadFeatures);handleCallQueue("autoloadFeatures",autoloadFeatures);function autoloadThrowOnFirstError(){autoloadFeatures(errors=>{if(errors){throw errors[0]}})}autoloadThrowOnFirstError();const observer=createElementObserver(autoloadThrowOnFirstError);observer.enable()});})();
