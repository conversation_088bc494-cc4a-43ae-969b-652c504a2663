!function(){"use strict";var t,e,n=function(){var t=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},i=function(t){if("loading"===document.readyState)return"loading";var e=n();if(e){if(t<e.domInteractive)return"loading";if(0===e.domContentLoadedEventStart||t<e.domContentLoadedEventStart)return"dom-interactive";if(0===e.domComplete||t<e.domComplete)return"dom-content-loaded"}return"complete"},r=function(t){var e=t.nodeName;return 1===t.nodeType?e.toLowerCase():e.toUpperCase().replace(/^#/,"")},o=function(t,e){var n="";try{for(;t&&9!==t.nodeType;){var i=t,o=i.id?"#"+i.id:r(i)+(i.classList&&i.classList.value&&i.classList.value.trim()&&i.classList.value.trim().length?"."+i.classList.value.trim().replace(/\s+/g,"."):"");if(n.length+o.length>(e||100)-1)return n||o;if(n=n?o+">"+n:o,i.id)break;t=i.parentNode}}catch(t){}return n},a=-1,s=function(){return a},c=function(t){addEventListener("pageshow",(function(e){e.persisted&&(a=e.timeStamp,t(e))}),!0)},u=function(){var t=n();return t&&t.activationStart||0},d=function(t,e){var i=n(),r="navigate";return s()>=0?r="back-forward-cache":i&&(document.prerendering||u()>0?r="prerender":document.wasDiscarded?r="restore":i.type&&(r=i.type.replace(/_/g,"-"))),{name:t,value:void 0===e?-1:e,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},l=function(t,e,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var i=new PerformanceObserver((function(t){Promise.resolve().then((function(){e(t.getEntries())}))}));return i.observe(Object.assign({type:t,buffered:!0},n||{})),i}}catch(t){}},h=function(t,e,n,i){var r,o;return function(a){e.value>=0&&(a||i)&&((o=e.value-(r||0))||void 0===r)&&(r=e.value,e.delta=o,e.rating=function(t,e){return t>e[1]?"poor":t>e[0]?"needs-improvement":"good"}(e.value,n),t(e))}},f=function(t){requestAnimationFrame((function(){return requestAnimationFrame((function(){return t()}))}))},p=function(t){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&t()}))},m=function(t){var e=!1;return function(){e||(t(),e=!0)}},g=-1,v=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},y=function(t){"hidden"===document.visibilityState&&g>-1&&(g="visibilitychange"===t.type?t.timeStamp:0,T())},S=function(){addEventListener("visibilitychange",y,!0),addEventListener("prerenderingchange",y,!0)},T=function(){removeEventListener("visibilitychange",y,!0),removeEventListener("prerenderingchange",y,!0)},_=function(){return g<0&&(g=v(),S(),c((function(){setTimeout((function(){g=v(),S()}),0)}))),{get firstHiddenTime(){return g}}},E=function(t){document.prerendering?addEventListener("prerenderingchange",(function(){return t()}),!0):t()},w=[1800,3e3],I=function(t,e){e=e||{},E((function(){var n,i=_(),r=d("FCP"),o=l("paint",(function(t){t.forEach((function(t){"first-contentful-paint"===t.name&&(o.disconnect(),t.startTime<i.firstHiddenTime&&(r.value=Math.max(t.startTime-u(),0),r.entries.push(t),n(!0)))}))}));o&&(n=h(t,r,w,e.reportAllChanges),c((function(i){r=d("FCP"),n=h(t,r,w,e.reportAllChanges),f((function(){r.value=performance.now()-i.timeStamp,n(!0)}))})))}))},C=[.1,.25],k=0,M=1/0,b=0,A=function(t){t.forEach((function(t){t.interactionId&&(M=Math.min(M,t.interactionId),b=Math.max(b,t.interactionId),k=b?(b-M)/7+1:0)}))},x=function(){return t?k:performance.interactionCount||0},L=function(){"interactionCount"in performance||t||(t=l("event",A,{type:"event",buffered:!0,durationThreshold:0}))},P=[],R=new Map,D=0,N=[],B=function(t){if(N.forEach((function(e){return e(t)})),t.interactionId||"first-input"===t.entryType){var e=P[P.length-1],n=R.get(t.interactionId);if(n||P.length<10||t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===n.entries[0].startTime&&n.entries.push(t);else{var i={id:t.interactionId,latency:t.duration,entries:[t]};R.set(i.id,i),P.push(i)}P.sort((function(t,e){return e.latency-t.latency})),P.length>10&&P.splice(10).forEach((function(t){return R.delete(t.id)}))}}},O=function(t){var e=self.requestIdleCallback||self.setTimeout,n=-1;return t=m(t),"hidden"===document.visibilityState?t():(n=e(t),p(t)),n},F=[200,500],j=function(t,e){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(e=e||{},E((function(){var n;L();var i,r=d("INP"),o=function(t){O((function(){t.forEach(B);var e=function(){var t=Math.min(P.length-1,Math.floor((x()-D)/50));return P[t]}();e&&e.latency!==r.value&&(r.value=e.latency,r.entries=e.entries,i())}))},a=l("event",o,{durationThreshold:null!==(n=e.durationThreshold)&&void 0!==n?n:40});i=h(t,r,F,e.reportAllChanges),a&&(a.observe({type:"first-input",buffered:!0}),p((function(){o(a.takeRecords()),i(!0)})),c((function(){D=x(),P.length=0,R.clear(),r=d("INP"),i=h(t,r,F,e.reportAllChanges)})))})))},q=[],z=[],H=0,U=new WeakMap,J=new Map,K=-1,Y=function(t){q=q.concat(t),V()},V=function(){K<0&&(K=O(G))},G=function(){J.size>10&&J.forEach((function(t,e){R.has(e)||J.delete(e)}));var t=P.map((function(t){return U.get(t.entries[0])})),e=z.length-50;z=z.filter((function(n,i){return i>=e||t.includes(n)}));for(var n=new Set,i=0;i<z.length;i++){var r=z[i];$(r.startTime,r.processingEnd).forEach((function(t){n.add(t)}))}var o=q.length-1-50;q=q.filter((function(t,e){return t.startTime>H&&e>o||n.has(t)})),K=-1};N.push((function(t){t.interactionId&&t.target&&!J.has(t.interactionId)&&J.set(t.interactionId,t.target)}),(function(t){var e,n=t.startTime+t.duration;H=Math.max(H,t.processingEnd);for(var i=z.length-1;i>=0;i--){var r=z[i];if(Math.abs(n-r.renderTime)<=8){(e=r).startTime=Math.min(t.startTime,e.startTime),e.processingStart=Math.min(t.processingStart,e.processingStart),e.processingEnd=Math.max(t.processingEnd,e.processingEnd),e.entries.push(t);break}}e||(e={startTime:t.startTime,processingStart:t.processingStart,processingEnd:t.processingEnd,renderTime:n,entries:[t]},z.push(e)),(t.interactionId||"first-input"===t.entryType)&&U.set(t,e),V()}));var $=function(t,e){for(var n,i=[],r=0;n=q[r];r++)if(!(n.startTime+n.duration<t)){if(n.startTime>e)break;i.push(n)}return i},W=[2500,4e3],X={},Q=[800,1800],Z=function t(e){document.prerendering?E((function(){return t(e)})):"complete"!==document.readyState?addEventListener("load",(function(){return t(e)}),!0):setTimeout(e,0)},tt=function(t,e){e=e||{};var i=d("TTFB"),r=h(t,i,Q,e.reportAllChanges);Z((function(){var o=n();o&&(i.value=Math.max(o.responseStart-u(),0),i.entries=[o],r(!0),c((function(){i=d("TTFB",0),(r=h(t,i,Q,e.reportAllChanges))(!0)})))}))};const et="2.1",nt="3",it={NO_VALUE:"",ACCEPTED:"1",DECLINED:"0"},rt={PREFERENCES:"p",ANALYTICS:"a",MARKETING:"m",SALE_OF_DATA:"t"},ot={MARKETING:"m",ANALYTICS:"a",PREFERENCES:"p",SALE_OF_DATA:"s"},at=()=>"undefined"!=typeof __CtaTestEnv__&&"true"===__CtaTestEnv__,st="_tracking_consent";function ct(t,e=!1){const n=document.cookie?document.cookie.split("; "):[];for(let e=0;e<n.length;e++){const[i,r]=n[e].split("=");if(t===decodeURIComponent(i)){return decodeURIComponent(r)}}if(e&&"_tracking_consent"===t&&!window.localStorage.getItem("tracking_consent_fetched")){if(at())return;return console.debug("_tracking_consent missing"),function(t="/"){const e=new XMLHttpRequest;e.open("HEAD",t,!1),e.withCredentials=!0,e.send()}(),window.localStorage.setItem("tracking_consent_fetched","true"),ct(t,!1)}}const ut="_cs";function dt(){const t=new URLSearchParams(window.location.search).get(ut)||ct(st);if(void 0!==t)return function(t){const e=t.slice(0,1);if("{"==e)return function(t){var e;let n;try{n=JSON.parse(t)}catch{return}if(n.v!==et)return;if(null===(e=n.con)||void 0===e||!e.CMP)return;return n}(t);if("3"==e)return function(t){const e=t.slice(1).split("_"),[n,i,r,o,a]=e;let s,c;try{s=e[5]?JSON.parse(e.slice(5).join("_")):void 0}catch{}if(a){const t=a.replace(/\*/g,"/").replace(/-/g,"+"),e=Array.from(atob(t)).map((t=>t.charCodeAt(0).toString(16).padStart(2,"0"))).join("");c=[8,13,18,23].reduce(((t,e)=>t.slice(0,e)+"-"+t.slice(e)),e)}function u(t){const e=n.split(".")[0];return e.includes(t.toLowerCase())?it.DECLINED:e.includes(t.toUpperCase())?it.ACCEPTED:it.NO_VALUE}function d(t){return n.includes(t.replace("t","s").toUpperCase())}return{v:nt,con:{CMP:{[ot.ANALYTICS]:u(ot.ANALYTICS),[ot.PREFERENCES]:u(ot.PREFERENCES),[ot.MARKETING]:u(ot.MARKETING),[ot.SALE_OF_DATA]:u(ot.SALE_OF_DATA)}},region:i||"",cus:s,purposes:{[rt.ANALYTICS]:d(rt.ANALYTICS),[rt.PREFERENCES]:d(rt.PREFERENCES),[rt.MARKETING]:d(rt.MARKETING),[rt.SALE_OF_DATA]:d(rt.SALE_OF_DATA)},sale_of_data_region:"t"==o,display_banner:"t"==r,consent_id:c}}(t);return}(t)}function lt(){return function(t){const e=dt();if(!e||!e.purposes)return!0;const n=e.purposes[t];return"boolean"!=typeof n||n}(rt.ANALYTICS)}function ht(){return lt()}const ft=10;const pt=Object.freeze({shop_domain:`${window.location.origin}/.well-known/shopify/monorail/v1/produce`,global:"https://monorail-edge.shopifysvc.com/v1/produce",canada:"https://monorail-edge-ca.shopifycloud.com/v1/produce",staging:"https://monorail-edge-staging.shopifycloud.com/v1/produce"}),mt=new RegExp(`^${window.location.origin}/((?:(?:[a-z]{2,3}|zh-hans|zh-hant)(?:-[a-zA-Z0-9]+)/)?cart/(add|change|update|clear))`),gt=new RegExp("cart-performance:|add:|change:|clear:|note_update:");var vt;function yt({monorailRegion:t,schema:e,rawData:n}){const i=Date.now(),r={schema_id:e,payload:e===vt.OnUnload?St(n):Tt(n),metadata:{event_created_at_ms:i,event_sent_at_ms:i}},o=pt[t||""],a=JSON.stringify(r);if(!o)return void console.debug("📡 Monorail: ",JSON.stringify(r,null,2));try{const t=new window.Blob([a],{type:"text/plain"});if("function"==typeof window.navigator.sendBeacon&&"function"==typeof window.Blob&&!function(){const{userAgent:t}=window.navigator;return-1!==t.lastIndexOf("iPhone; CPU iPhone OS 12_")||-1!==t.lastIndexOf("iPad; CPU OS 12_")}()&&window.navigator.sendBeacon(o,t))return}catch(t){}const s=new XMLHttpRequest;s.open("POST",o),s.setRequestHeader("Content-type","text/plain"),s.send(a)}function St(t){const e=_t(t,["domInteractive","domContentLoadedEventStart","domContentLoadedEventEnd","domComplete","firstPaint","visuallyReady","initiatorType","redirectCount","cartAjaxResourceTimings","cartPerformanceMetrics"]);return{perf_kit_init:t.perfKitInit,perf_kit_version:t.perfKitVersion,url:t.url,page_type:t.pageType,shop_id:t.shopId,application:t.application,storefront_id:t.storefrontId,theme_instance_id:t.themeInstanceId,theme_name:t.themeName,theme_version:t.themeVersion,session_token:t.sessionToken,unique_token:t.uniqueToken,micro_session_id:t.microSessionId,micro_session_count:t.microSessionCount,cumulative_layout_shift:t.cumulativeLayoutShift,cumulative_layout_shift_target:t.cumulativeLayoutShiftTarget,first_contentful_paint:t.firstContentfulPaint,largest_contentful_paint:t.largestContentfulPaint,largest_contentful_paint_target:t.largestContentfulPaintTarget,time_to_first_byte:t.timeToFirstByte,seo_bot:t.seoBot,humanness_score:t.humannessScore,ja3_fingerprint:t.ja3Fingerprint,navigation_start:t.navigationStart,navigation_type:t.navigationType,navigation_bad:t.navigationBad,encoded_body_size:t.encodedBodySize,decoded_body_size:t.decodedBodySize,transfer_size:t.transferSize,first_interim_response_start:t.firstInterimResponseStart,final_response_headers_start:t.finalResponseHeadersStart,response_start:t.responseStart,response_end:t.responseEnd,worker_start:t.workerStart,connect_start:t.connectStart,connect_end:t.connectEnd,domain_lookup_start:t.domainLookupStart,domain_lookup_end:t.domainLookupEnd,fetch_start:t.fetchStart,redirect_start:t.redirectStart,redirect_end:t.redirectEnd,request_start:t.requestStart,secure_connection_start:t.secureConnectionStart,next_hop_protocol:t.nextHopProtocol,server_timing:t.serverTiming,paint_timing_hidden:t.paintTimingHidden,referrer:t.referrer,render_region:t.renderRegion,resource_timing:t.resourceTiming,other_metrics:JSON.stringify(e)}}function Tt(t){const e=_t(t,["longAnimationFrame"]);return{url:t.url,page_type:t.pageType,shop_id:t.shopId,application:t.application,storefront_id:t.storefrontId,theme_instance_id:t.themeInstanceId,session_token:t.sessionToken,unique_token:t.uniqueToken,micro_session_id:t.microSessionId,micro_session_count:t.microSessionCount,interaction_to_next_paint:t.interactionToNextPaint,interaction_to_next_paint_target:t.interactionToNextPaintTarget,seo_bot:t.seoBot,humanness_score:t.humannessScore,ja3_fingerprint:t.ja3Fingerprint,referrer:t.referrer,worker_start:t.workerStart,next_hop_protocol:t.nextHopProtocol,navigation_bad:t.navigationBad,other_interaction_metrics:JSON.stringify(e)}}function _t(t,e){return e.reduce(((e,n)=>{var i;return t[n]&&(e[(i=n,i.replace(/[A-Z]/g,(t=>`_${t.toLowerCase()}`)))]=t[n]||null),e}),{})}!function(t){t.OnInteraction="perf_kit_on_interaction/3.1",t.OnUnload="perf_kit_on_unload/3.3"}(vt||(vt={}));const Et="xxxx-4xxx-xxxx-xxxxxxxxxxxx";function wt(){let t="";try{const e=window.crypto,n=new Uint16Array(31);e.getRandomValues(n);let i=0;t=Et.replace(/[x]/g,(t=>{const e=n[i]%16;return i++,("x"===t?e:3&e|8).toString(16)})).toUpperCase()}catch(e){t=Et.replace(/[x]/g,(t=>{const e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)})).toUpperCase()}return`${function(){let t=0,e=0;t=(new Date).getTime()>>>0;try{e=performance.now()>>>0}catch(t){e=0}return Math.abs(t+e).toString(16).toLowerCase().padStart(8,"0")}()}-${t}`}const It=Pt;!function(t,e){const n=Pt,i=t();for(;;)try{if(474310===-parseInt(n(327))/1*(-parseInt(n(233))/2)+parseInt(n(256))/3*(parseInt(n(184))/4)+-parseInt(n(305))/5+parseInt(n(320))/6*(-parseInt(n(331))/7)+parseInt(n(311))/8+-parseInt(n(191))/9*(-parseInt(n(186))/10)+-parseInt(n(316))/11*(parseInt(n(182))/12))break;i.push(i.shift())}catch(t){i.push(i.shift())}}(Mt);const Ct=function(){let t=!0;return function(e,n){const i=t?function(){if(n){const t=n[Pt(290)](e,arguments);return n=null,t}}:function(){};return t=!1,i}}(),kt=Ct(this,(function(){const t=Pt;return kt[t(309)]()[t(194)]("(((.+)+)+)+$")[t(309)]()[t(322)](kt)[t(194)](t(228))}));function Mt(){const t=["addEventListener","checkViewportRatio","atan2","abs","scrollEvents","requestIdleCallbackId","updateCookie","(((.+)+)+)+$","min","removeEventListener","getBoundingClientRect","toDataURL","853354TYdEgG","log","getLegacy","keydown","ses","options","canvas","round","getDistance","timeStamp","down","lastTouchEvent","preparePayloadData","Shopify Storefront","touchend","getContext","updateIntervalId","init","lastUpdateTime","documentElement","int","mousePositions","parse","3qkSQNC","undefined","keyActivity","debug","keyEvents","blue","keyboardBursts","cookieExpiry","updateInterval","test","move","; path=","sessionStart","touchstart","fillText","innerHeight","(^| )","catch","window","then","requestIdleCallback","checkBrowserConsistency","cookieStore","=([^;]+)","calculateClickPrecision","dir","cookieJar","DOMContentLoaded","Control","touchActivity","toLowerCase","setLegacy","cookieName","eventHandlers","apply","16px Arial","[SHS] ","runCanvasTest","cancelIdleCallback","top","cookie","font","config","sessionData","target","environment","state","length","createElement","3672545uNGljY","floor","addColorStop","readyState","toString","createLinearGradient","3973088iWjxBt","loadSessionData","start","keyboardBurstActivity","Mobile","22QtHQpI","set","push","updateSessionData","390594WQZsRL","checkEnvironment","constructor","touchEvents","sqrt","get","loading","1yjMuXO","height","value","Meta","49EbpwRJ","touchmove","keep_alive","pre","pageCount","scrollY","browserAPI","shift","3824556tvddKC","now","3412012xfAalR","key","10hBAvyt","webdriver","max","destroy","path","4730598daKBLM","monitorBehavior","clickActivity","search","clientWidth","navigator","mouseActivity","red","startTime","languages","touches","document","clickPositions","plugins","scrollActivity","click","setTimeout","reduce","clientX","clientY","fillStyle","width","environmentChecked","getTimezoneOffset","devicePixelRatio","duration","sameSite","mousemove","clearTimeout","ontouchstart"];return(Mt=function(){return t})()}kt();class bt{[It(180)];options;constructor(t,e){const n=It;this[n(180)]=t,this[n(238)]={path:"/",sameSite:"Strict",debug:!1,...e}}async[It(325)](){const t=It,e=this.options.cookieName;if(this.browserAPI.cookieStore)try{const n=await this[t(180)][t(278)][t(325)]({name:e});return n?n[t(329)]:null}catch(e){return this[t(235)]()}return this.getLegacy()}async set(t){const e=It;if(this[e(180)][e(278)])try{await this[e(180)][e(278)][e(317)]({name:this.options.cookieName,value:t,path:this[e(238)][e(190)],maxAge:this.options.cookieExpiry,sameSite:this[e(238)][e(217)]?.[e(286)]()})[e(273)]((n=>{this[e(287)](t)}))}catch(n){this[e(287)](t)}else this.setLegacy(t)}getLegacy(){const t=It,e=this[t(238)][t(288)],n=new RegExp(t(272)+e+t(279)).exec(this[t(180)][t(202)][t(296)]);return n?n[2]:null}setLegacy(t){const e=It;this.browserAPI[e(202)].cookie=this.options[e(288)]+"="+t+e(267)+this.options[e(190)]+"; max-age="+this[e(238)][e(263)]+"; SameSite="+this[e(238)][e(217)]}log(t){const e=It;this[e(238)].debug&&console[e(234)]("[CookieJar] "+t)}}const At={document:document,navigator:navigator,window:window,screen:screen,cookieStore:typeof window!==It(257)&&"cookieStore"in window?window[It(278)]:void 0,performance:typeof performance!==It(257)?performance:void 0};function xt(t){const e=It;if(t[e(303)]<2)return{speed:0,jitter:0};const n=t[e(208)](((t,e)=>t+e.s),0)/t[e(303)],i=t.reduce(((t,e)=>t+(e.s-n)**2),0)/(t[e(303)]-1),r=Math[e(324)](i);return{speed:n,jitter:r}}function Lt(t,e,n,i,r,o,a,s,c,u,d,l,h){const f=It,p=Math[f(229)](1e3,t),m=Math.min(1e3,n),g=Math[f(229)](1e3,o),v=Math[f(229)](1e3,s),y=Math[f(229)](1e3,a),S=Math[f(229)](1e3,c),T=[];if(e[f(303)]>=3)for(let t=1;t<e[f(303)];t++){const n=e[t].x-e[t-1].x,i=e[t].y-e[t-1].y,r=e[t].t-e[t-1].t;if(0===r)continue;const o=Math[f(324)](n*n+i*i)/r;T[f(318)]({s:o})}const _=function(t){const e=It;if(t.length<2)return!1;let n=!1,i=null;for(const r of t)if(null!==i){if(Math[e(224)](r.s-i)>5e-4){n=!0;break}i=r.s}else i=r.s;return n}(T),E=function(t){const e=It;if(t.length<3)return 0;let n=0,i=0;for(let r=1;r<t.length;r++){const o=t[r-1],a=t[r],s=t[r+1];if(!o||!a||!s)continue;const c=Math[e(223)](a.y-o.y,a.x-o.x),u=Math[e(223)](s.y-a.y,s.x-a.x),d=Math[e(224)](u-c);(d<Math.PI/12||d>2*Math.PI-Math.PI/12)&&n++,i++}return i>0?n/i:0}(e),{speed:w,jitter:I}=xt(T);let C=0,k=0;if(i[f(303)]>1){C=new Set(i.map((t=>Math[f(306)](t.x/20)+","+Math[f(306)](t.y/20)))).size>1?1:0,k=i.reduce(((t,e)=>t+e[f(334)]),0)/i[f(303)]}const M=r[f(208)](((t,e)=>(null===e[f(253)]||t.push(e.int),t)),[]),{rhythmConsistency:b,jitter:A}=function(t){const e=It;if(t.length<2)return{rhythmConsistency:0,jitter:0};const n=t[e(208)](((t,e)=>t+e),0)/t.length,i=t[e(208)](((t,e)=>(t??0)+((e??0)-n)**2),0)/t[e(303)],r=Math[e(324)](i);return{rhythmConsistency:0===r?1:Math[e(188)](0,1-r/n),jitter:r/n}}(M),x=M.length>0?M[f(208)](((t,e)=>t+e),0)/M.length:0,L=[];if(u.length>=3)for(let t=1;t<u[f(303)];t++){const e=Math[f(224)](u[t].p-u[t-1].p),n=Math.max(1,u[t].t-u[t-1].t);if(0===n)continue;const i=e/n,r=u[t].p>u[t-1].p?f(243):"up";L[f(318)]({s:i,dir:r,t:u[t].t})}const{speed:P,jitter:R}=xt(L),D=function(t){const e=It;if(t[e(303)]<2)return 0;let n=0;for(let i=1;i<t[e(303)];i++){const r=t[i-1],o=t[i];Math[e(224)](o.s-r.s)<=(o.s+r.s)/2*2&&n++,o[e(281)]===r[e(281)]&&n++}return n/(2*(t.length-1))}(L),N=Math.min(1e3,function(t){const e=It;if(t.length<2)return 0;let n=0,i=0;for(let r=1;r<t[e(303)];r++)t[r].t-t[r-1].t>=500&&t[r].t-i>=500&&(n++,i=t[r].t);return n}(L)),{speed:B,jitter:O}=xt(d),F=function(t){const e=It;if(t[e(303)]<2)return 0;let n=0;for(let i=1;i<t.length;i++){const r=t[i-1],o=t[i];Math[e(224)](o.s-r.s)<=50&&n++}return n/(t.length-1)}(d),j=Math.min(1e3,function(t){const e=It;if(t.length<2)return 0;let n=0;for(let i=1;i<t[e(303)];i++)t[i].t-t[i-1].t>=500&&n++;return n}(d)),q=Math.floor((h-l)/1e3);return{ma:p,ca:m,ka:g,sa:v,ta:S,kba:y,t:Math[f(229)](3600,q),nm:_?1:0,ms:Dt(E),mj:Dt(I),msp:Dt(w),vc:C,cp:Dt(k),rc:Dt(b),kj:Dt(A),ki:Dt(x),ss:Dt(P),sj:Dt(R),ssm:Dt(D),sp:N,ts:Dt(B),tj:Dt(O),tp:j,tsm:Dt(F)}}function Pt(t,e){const n=Mt();return Pt=function(t,e){return n[t-=179]},Pt(t,e)}function Rt(t,e){let n=0;return(...i)=>{const r=Pt,o=typeof performance!==r(257)?performance[r(183)]():Date[r(183)]();o-n>=e&&(n=o,t(...i))}}function Dt(t){return Math[It(240)](100*t)/100}class Nt{config;[It(302)];browserAPI;[It(282)];constructor(t,e=At){const n=It,i={cookieName:n(333),cookieExpiry:1800,updateInterval:5e3,debug:!1};this[n(298)]={...i,...t},this[n(180)]=e,this[n(282)]=new bt(this[n(180)],{cookieName:this[n(298)].cookieName,cookieExpiry:this[n(298)][n(263)],debug:this[n(298)][n(259)]}),this[n(302)]={startTime:Date[n(183)](),lastUpdateTime:null,environmentChecked:!1,sessionData:null,mouseActivity:0,clickActivity:0,keyActivity:0,keyboardBurstActivity:0,scrollActivity:0,touchActivity:0,mousePositions:[],clickPositions:[],keyEvents:[],scrollEvents:[],keyboardBursts:[],touchEvents:[],lastTouchEvent:null}}init(){(async()=>{const t=Pt;await this[t(312)](),await this[t(321)](),await this.updateCookie(!0),this.browserAPI[t(274)][t(207)]((()=>{this[t(192)]()}),100);const e=()=>{const n=t,i=Date.now(),r=i-(this[n(302)][n(251)]||i),o=Math.max(0,this[n(298)][n(264)]-r),a=()=>{const t=n;this[t(180)][t(274)].requestIdleCallback?this[t(302)][t(226)]=this[t(180)][t(274)][t(276)]((()=>{const n=t;this[n(227)]()[n(275)]((()=>{e()}))}),{timeout:2e3}):this[t(227)]()[t(275)]((()=>{e()}))};o>0?this[n(180)][n(274)][n(207)](a,o):a()};e()})()}[It(189)](){const t=It;this[t(302)][t(249)]&&this[t(180)].window[t(219)](this[t(302)][t(249)]),this[t(302)][t(226)]&&this[t(180)][t(274)][t(294)]&&this[t(180)][t(274)][t(294)](this[t(302)][t(226)]),this[t(302)][t(289)]&&(this[t(180)][t(202)][t(230)](t(218),this[t(302)][t(289)][t(218)]),this[t(180)].document[t(230)]("click",this[t(302)][t(289)].click),this.browserAPI[t(202)][t(230)](t(236),this[t(302)].eventHandlers[t(236)]),this.browserAPI[t(202)][t(230)]("scroll",this.state[t(289)].scroll),t(220)in window&&this[t(302)].eventHandlers.touchstart&&this.state[t(289)][t(332)]&&this[t(302)][t(289)][t(247)]&&(this[t(180)][t(202)][t(230)](t(269),this[t(302)][t(289)][t(269)]),this[t(180)][t(202)][t(230)](t(332),this.state[t(289)][t(332)]),this[t(180)].document[t(230)]("touchend",this[t(302)][t(289)][t(247)])))}async[It(312)](){const t=It,e=function(t){const e=It;if(!t)return null;try{const n=JSON[e(255)](atob(decodeURIComponent(t)));return n.v&&n.ts?n:null}catch(t){return null}}(await this[t(282)].get());e&&(this[t(302)][t(251)]=e.ts,e[t(237)]&&(this.state[t(299)]={pageCount:e[t(237)].p||1,sessionStart:e[t(237)].s||Date[t(183)](),duration:e[t(237)].d||0}))}[It(321)](){const t=It,e={wd:this.browserAPI[t(196)][t(187)]?1:0,ua:/bot|crawler|spider|scraper/i[t(265)](this[t(180)][t(196)].userAgent)?0:1,cv:this[t(293)]()?1:0,br:this[t(277)]()?1:0};return this[t(302)][t(301)]=e,this[t(302)][t(213)]=!0,e}[It(293)](){const t=It;try{const e=this.browserAPI[t(202)][t(304)](t(239));e[t(212)]=200,e[t(328)]=50;const n=e[t(248)]("2d");if(null===n)return!1;const i=n[t(310)](0,0,200,0);i[t(307)](0,t(198)),i[t(307)](1,t(261)),n.fillStyle=i,n.fillRect(0,0,200,50),n[t(211)]="#fff",n[t(297)]=t(291),n[t(270)](t(246),33,30);const r=e[t(232)]();return Boolean(r&&r.length>50)}catch(t){return!1}}[It(277)](){const t=It;let e=0;this[t(180)][t(196)].languages&&this[t(180)][t(196)][t(200)][t(303)]>0&&e++,(this.browserAPI[t(196)][t(204)]&&this[t(180)][t(196)].plugins[t(303)]>0||this.browserAPI.navigator.userAgent.includes(t(315)))&&e++,this[t(222)]()&&e++,void 0!==this[t(180)][t(274)][t(215)]&&e++;try{const n=(new Date)[t(214)]();n>-840&&n<840&&e++}catch(t){}return e>=3}[It(222)](){const t=It,e=this[t(180)].window.innerWidth||this[t(180)].document[t(252)][t(195)],n=this[t(180)][t(274)][t(271)]||this.browserAPI[t(202)].documentElement.clientHeight;if(!e||!n)return!1;if(e<=2||n<=2)return!1;const i=e/n;return i>=.2&&i<=5}[It(192)](){const t=It,e=Rt((t=>{const e=Pt;this[e(302)].mouseActivity++,this[e(302)][e(254)][e(303)]>=10&&this.state[e(254)][e(181)](),this.state.mousePositions[e(318)]({x:t[e(209)],y:t.clientY,t:t[e(242)]})}),100),n=t=>{const e=Pt;this[e(302)].clickActivity++,requestAnimationFrame((()=>setTimeout((()=>{const n=e;this[n(302)][n(203)].length>=5&&this[n(302)].clickPositions.shift(),this.state[n(203)].push({x:t.clientX,y:t.clientY,t:t[n(242)],pre:this.calculateClickPrecision(t)})}),0)))},i=t=>{const e=Pt;this[e(302)][e(258)]++;const n=t[e(185)];if(["Shift",e(284),"Alt",e(330)].includes(n))return;const i=this[e(302)][e(260)][this.state[e(260)].length-1],r=t.timeStamp,o=i?r-i.t:null;this[e(302)][e(260)][e(303)]>=10&&this[e(302)][e(260)].shift(),this[e(302)][e(260)][e(318)]({k:n,t:r,int:o}),null!==o&&o<100?(this[e(302)][e(262)][e(318)](o),this[e(302)].keyboardBursts[e(303)]>=5&&(this[e(302)][e(314)]++,this[e(302)].keyboardBursts=[])):this[e(302)].keyboardBursts=[]},r=Rt((t=>{const e=Pt;this[e(302)][e(205)]++;const n=t[e(242)],i=window[e(179)];this[e(302)][e(225)][e(303)]>=10&&this[e(302)].scrollEvents[e(181)](),this[e(302)][e(225)].push({p:i,t:n})}),200),o=t=>{const e=Pt;this[e(302)][e(285)]++,this[e(302)][e(323)][e(303)]>=10&&this[e(302)][e(323)][e(181)](),this[e(302)][e(244)]={x:t[e(201)][0][e(209)],y:t[e(201)][0][e(210)],t:t[e(242)],typ:e(313),s:0,d:0}},a=Rt((t=>{const e=Pt,n={x:t[e(201)][0][e(209)],y:t[e(201)][0][e(210)]};if(this.state[e(244)]){const i=this[e(241)](this[e(302)][e(244)],n);this[e(302)][e(323)][e(303)]>=10&&this.state[e(323)][e(181)]();const r={x:t[e(201)][0][e(209)],y:t.touches[0][e(210)],t:t[e(242)],typ:e(266),d:i,s:i/(t[e(242)]-this[e(302)].lastTouchEvent.t)};this.state[e(323)][e(318)](r),this.state[e(244)]=r}else{const n={x:t.touches[0][e(209)],y:t.touches[0][e(210)],t:t[e(242)],typ:"move",s:0,d:0};this[e(302)][e(244)]=n}}),200),s=t=>{this.state.lastTouchEvent=null};this.browserAPI.document.addEventListener("mousemove",e,{passive:!0}),this.browserAPI.document.addEventListener(t(206),n,{passive:!0}),this.browserAPI.document[t(221)](t(236),i,{passive:!0}),this.browserAPI[t(202)][t(221)]("scroll",r,{passive:!0}),t(220)in window&&(this[t(180)].document[t(221)](t(269),o,{passive:!0}),this[t(180)][t(202)][t(221)](t(332),a,{passive:!0}),this[t(180)][t(202)][t(221)]("touchend",s,{passive:!0})),this[t(302)][t(289)]={mousemove:e,click:n,keydown:i,scroll:r,touchstart:o,touchmove:a,touchend:s}}async[It(227)](t=!1){const e=It;this[e(319)](t);const n=this[e(245)]();n&&(await this.cookieJar.set(n),this[e(302)][e(251)]=Date[e(183)]())}updateSessionData(t=!1){const e=It,n=Date[e(183)]();let i=Math[e(306)]((n-this[e(302)][e(199)])/1e3);null!==this[e(302)][e(251)]&&(i=Math[e(306)]((n-this[e(302)][e(251)])/1e3)),this[e(302)][e(299)]?(this.state.sessionData.pageCount+=t?1:0,this[e(302)][e(299)][e(216)]+=i):this[e(302)].sessionData={pageCount:1,sessionStart:this[e(302)][e(199)],duration:0}}[It(245)](){const t=It;if(!this[t(302)][t(213)]&&0===this[t(302)].mouseActivity&&0===this.state[t(193)])return null;const e=this.state[t(301)]??{wd:0,ua:1,cv:0,br:0},n=Lt(this[t(302)][t(197)],this[t(302)][t(254)],this[t(302)][t(193)],this[t(302)].clickPositions,this[t(302)][t(260)],this.state[t(258)],this[t(302)][t(314)],this[t(302)][t(205)],this[t(302)][t(285)],this.state[t(225)],this[t(302)][t(323)],this[t(302)][t(199)],Date[t(183)]()),i={p:this[t(302)].sessionData?.[t(335)]??1,s:this.state[t(299)]?.[t(268)]??this.state[t(199)],d:this[t(302)][t(299)]?.duration??0},r={v:2,ts:Date.now(),env:e,bhv:n,ses:i};return encodeURIComponent(btoa(JSON.stringify(r)))}[It(234)](t){const e=It;this.config[e(259)]&&console[e(234)](e(292)+t)}[It(280)](t){const e=It,n=t[e(300)];if(!(n&&n instanceof Element&&n[e(231)]))return 0;const i=n[e(231)]();if(!i[e(212)]||!i[e(328)])return 0;const r=i.left+i[e(212)]/2,o=i[e(295)]+i[e(328)]/2,a=this[e(241)]({x:t[e(209)],y:t[e(210)]},{x:r,y:o}),s=Math.sqrt(i.width**2+i[e(328)]**2)/2;return s>0?Math[e(188)](0,1-a/s):0}[It(241)](t,e){return Math[It(324)]((e.x-t.x)**2+(e.y-t.y)**2)}}const Bt=(t,e=At)=>{const n=It,i=new Nt(t,e);return e[n(202)][n(308)]===n(326)?e[n(202)][n(221)](n(283),(()=>i[n(250)]())):i[n(250)](),i};let Ot=!0,Ft=!1,jt=null,qt=!1;class zt{info;config;performanceMetrics;constructor(t){const e=/_shopify_s=([^;]*)/.exec(document.cookie),n=e?e[1]:void 0,i=/_shopify_y=([^;]*)/.exec(document.cookie),r=i?i[1]:void 0;this.config=t,this.info={perfKitInit:Date.now(),perfKitVersion:"1.6.6",url:window.location.href,referrer:document.referrer||void 0,microSessionId:wt(),microSessionCount:0,sessionToken:n,uniqueToken:r},performance.setResourceTimingBufferSize(1e3),this.performanceMetrics=function(t){const e=performance.getEntriesByType("navigation");if(0===e.length)return{};const n=e[0];let i=!1;return(n.requestStart&&n.startTime&&n.requestStart<n.startTime||n.responseStart&&n.startTime&&n.responseStart<n.startTime||n.responseStart&&n.fetchStart&&n.responseStart<n.fetchStart||n.startTime&&n.fetchStart<n.startTime||n.responseEnd&&n.responseEnd>t+864e5)&&(i=!0),{encodedBodySize:n.encodedBodySize,decodedBodySize:n.decodedBodySize,navigationStart:Math.round(n.startTime),navigationType:n.type,navigationBad:i,firstInterimResponseStart:Math.round(n.firstInterimResponseStart),finalResponseHeadersStart:Math.round(n.finalResponseHeadersStart),responseStart:Math.round(n.responseStart),responseEnd:Math.round(n.responseEnd),workerStart:Math.round(n.workerStart),connectStart:Math.round(n.connectStart),connectEnd:Math.round(n.connectEnd),domainLookupStart:Math.round(n.domainLookupStart),domainLookupEnd:Math.round(n.domainLookupEnd),fetchStart:Math.round(n.fetchStart),redirectStart:Math.round(n.redirectStart),redirectEnd:Math.round(n.redirectEnd),requestStart:Math.round(n.requestStart),secureConnectionStart:Math.round(n.secureConnectionStart),nextHopProtocol:n.nextHopProtocol,serverTiming:JSON.stringify(n.serverTiming),domInteractive:Math.round(n.domInteractive),domComplete:Math.round(n.domComplete),domContentLoadedEventStart:Math.round(n.domContentLoadedEventStart),domContentLoadedEventEnd:Math.round(n.domContentLoadedEventEnd),redirectCount:n.redirectCount,initiatorType:n.initiatorType,transferSize:n.transferSize}}(this.info.perfKitInit)}}const Ht=new Set;function Ut(){if(null!==jt){if(Jt()&&Ht.size>0){let t={};for(const e of Ht)t={...t,...e};Ht.clear(),jt.info.microSessionCount+=1;const e=function(t){if(!(100*Math.random()>(t||ft)))return performance.getEntriesByType("resource").map((t=>{const e=Object.entries(t.toJSON()).map((([t,e])=>"number"==typeof e?[t,Math.round(e)]:[t,e]));return JSON.stringify(Object.fromEntries(e))}))}(jt.config.resourceTimingSamplingRate),n=performance.getEntriesByType("resource").filter((t=>mt.test(t.name))),i=performance.getEntriesByType("measure").filter((t=>gt.test(t.name))).map((t=>{const e=t.toJSON();return e.name.startsWith("cart-performance:")||(e.name=`cart-performance:${e.name}`),e}));yt({monorailRegion:jt.config.monorailRegion,schema:vt.OnUnload,rawData:{...jt.info,...jt.config.storefrontData,...jt.performanceMetrics,...t,resourceTiming:e,cartAjaxResourceTimings:n,cartPerformanceMetrics:i,paintTimingHidden:qt}})}}else console.debug("⛔️ Shopify/perf-kit is not initialized")}function Jt(){return ht()}!function(t,e){!function(t,e){e=e||{},I(m((function(){var n,i=d("CLS",0),r=0,o=[],a=function(t){t.forEach((function(t){if(!t.hadRecentInput){var e=o[0],n=o[o.length-1];r&&t.startTime-n.startTime<1e3&&t.startTime-e.startTime<5e3?(r+=t.value,o.push(t)):(r=t.value,o=[t])}})),r>i.value&&(i.value=r,i.entries=o,n())},s=l("layout-shift",a);s&&(n=h(t,i,C,e.reportAllChanges),p((function(){a(s.takeRecords()),n(!0)})),c((function(){r=0,i=d("CLS",0),n=h(t,i,C,e.reportAllChanges),f((function(){return n()}))})),setTimeout(n,0))})))}((function(e){var n=function(t){var e,n={};if(t.entries.length){var r=t.entries.reduce((function(t,e){return t&&t.value>e.value?t:e}));if(r&&r.sources&&r.sources.length){var a=(e=r.sources).find((function(t){return t.node&&1===t.node.nodeType}))||e[0];a&&(n={largestShiftTarget:o(a.node),largestShiftTime:r.startTime,largestShiftValue:r.value,largestShiftSource:a,largestShiftEntry:r,loadState:i(r.startTime)})}}return Object.assign(t,{attribution:n})}(e);t(n)}),e)}((function(t){const{attribution:e,value:n}=t;Ht.add({cumulativeLayoutShift:n,cumulativeLayoutShiftTarget:e.largestShiftTarget})})),function(t,e){!function(t,e){e=e||{},E((function(){var n,i=_(),r=d("LCP"),o=function(t){e.reportAllChanges||(t=t.slice(-1)),t.forEach((function(t){t.startTime<i.firstHiddenTime&&(r.value=Math.max(t.startTime-u(),0),r.entries=[t],n())}))},a=l("largest-contentful-paint",o);if(a){n=h(t,r,W,e.reportAllChanges);var s=m((function(){X[r.id]||(o(a.takeRecords()),a.disconnect(),X[r.id]=!0,n(!0))}));["keydown","click"].forEach((function(t){addEventListener(t,(function(){return O(s)}),{once:!0,capture:!0})})),p(s),c((function(i){r=d("LCP"),n=h(t,r,W,e.reportAllChanges),f((function(){r.value=performance.now()-i.timeStamp,X[r.id]=!0,n(!0)}))}))}}))}((function(e){var i=function(t){var e={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:t.value};if(t.entries.length){var i=n();if(i){var r=i.activationStart||0,a=t.entries[t.entries.length-1],s=a.url&&performance.getEntriesByType("resource").filter((function(t){return t.name===a.url}))[0],c=Math.max(0,i.responseStart-r),u=Math.max(c,s?(s.requestStart||s.startTime)-r:0),d=Math.max(u,s?s.responseEnd-r:0),l=Math.max(d,a.startTime-r);e={element:o(a.element),timeToFirstByte:c,resourceLoadDelay:u-c,resourceLoadDuration:d-u,elementRenderDelay:l-d,navigationEntry:i,lcpEntry:a},a.url&&(e.url=a.url),s&&(e.lcpResourceEntry=s)}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)}((function(t){const{attribution:e,value:n}=t;Ht.add({largestContentfulPaint:Math.round(n),largestContentfulPaintTarget:e.element})})),function(t,e){I((function(e){var r=function(t){var e={timeToFirstByte:0,firstByteToFCP:t.value,loadState:i(s())};if(t.entries.length){var r=n(),o=t.entries[t.entries.length-1];if(r){var a=r.activationStart||0,c=Math.max(0,r.responseStart-a);e={timeToFirstByte:c,firstByteToFCP:t.value-c,loadState:i(t.entries[0].startTime),navigationEntry:r,fcpEntry:o}}}return Object.assign(t,{attribution:e})}(e);t(r)}),e)}((function(t){const{value:e}=t;Ht.add({firstContentfulPaint:Math.round(e)})})),function(t,n){e||(e=l("long-animation-frame",Y)),j((function(e){var n=function(t){var e=t.entries[0],n=U.get(e),r=e.processingStart,a=n.processingEnd,s=n.entries.sort((function(t,e){return t.processingStart-e.processingStart})),c=$(e.startTime,a),u=t.entries.find((function(t){return t.target})),d=u&&u.target||J.get(e.interactionId),l=[e.startTime+e.duration,a].concat(c.map((function(t){return t.startTime+t.duration}))),h=Math.max.apply(Math,l),f={interactionTarget:o(d),interactionTargetElement:d,interactionType:e.name.startsWith("key")?"keyboard":"pointer",interactionTime:e.startTime,nextPaintTime:h,processedEventEntries:s,longAnimationFrameEntries:c,inputDelay:r-e.startTime,processingDuration:a-r,presentationDelay:Math.max(h-a,0),loadState:i(e.startTime)};return Object.assign(t,{attribution:f})}(e);t(n)}),n)}((function(t){const{attribution:e,value:n}=t;if(null===jt)return void console.debug("⛔️ Shopify/perf-kit is not initialized");if(!Jt())return;jt.info.microSessionCount+=1;const{inputDelay:i,processingDuration:r,presentationDelay:o,longAnimationFrameEntries:a,interactionTarget:s}=e,c=a.at(-1);let u=c?.scripts?.[0];c?.scripts?.forEach((t=>{(!u||t.duration>u.duration)&&(u=t)})),yt({monorailRegion:jt.config.monorailRegion,schema:vt.OnInteraction,rawData:{...jt.info,...jt.config.storefrontData,...jt.performanceMetrics,interactionToNextPaint:Math.round(n),interactionToNextPaintTarget:s,longAnimationFrame:{input_delay:Math.round(i),processing_duration:Math.round(r),presentation_delay:Math.round(o),...u?{slowest_script:u.sourceURL,slowest_script_duration:Math.round(u.duration)}:{}}}})}),{reportAllChanges:!0}),function(t,e){tt((function(e){var n=function(t){var e={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(t.entries.length){var n=t.entries[0],i=n.activationStart||0,r=Math.max((n.workerStart||n.fetchStart)-i,0),o=Math.max(n.domainLookupStart-i,0),a=Math.max(n.connectStart-i,0),s=Math.max(n.connectEnd-i,0);e={waitingDuration:r,cacheDuration:o-r,dnsDuration:a-o,connectionDuration:s-a,requestDuration:t.value-s,navigationEntry:n}}return Object.assign(t,{attribution:e})}(e);t(n)}),e)}((function(t){const{value:e}=t;Ht.add({timeToFirstByte:Math.round(e)})})),addEventListener("DOMContentLoaded",(()=>{qt="hidden"===document.visibilityState})),addEventListener("visibilitychange",(()=>{"hidden"===document.visibilityState&&Ut()})),function(){const{dataset:t}=document.currentScript;try{e=function(t){if(!t.application)throw new Error("Application is missing");if(!["storefront-renderer","hydrogen"].includes(t.application.toLowerCase()))throw new Error("Invalid application type");if(!t.shopId)throw new Error("shopId is missing");if(!t.themeInstanceId&&!t.storefrontId)throw new Error("Either `themeInstanceId` or `storefrontId` must be defined");for(const e of["shopId","humannessScore","themeInstanceId","storefrontId"])if(t[e]&&isNaN(Number(t[e])))throw new Error(`Invalid ${e}`);if(t.monorailRegion&&!["shop_domain","global","staging","canada"].includes(t.monorailRegion.toLowerCase()))throw new Error("Invalid monorail region");if(t.resourceTimingSamplingRate&&(isNaN(Number(t.resourceTimingSamplingRate))||Number(t.resourceTimingSamplingRate)<ft||Number(t.resourceTimingSamplingRate)>100))throw new Error("Invalid resource timing sampling rate");return{storefrontData:{application:t.application.toLowerCase(),shopId:Number(t.shopId),renderRegion:t.renderRegion,pageType:t.pageType,seoBot:"true"===t.seoBot,humannessScore:Number(t.humannessScore)||void 0,ja3Fingerprint:t.ja3Fingerprint,themeInstanceId:Number(t.themeInstanceId)||void 0,storefrontId:Number(t.storefrontId)||void 0,themeName:t.themeName||void 0,themeVersion:t.themeVersion||void 0},monorailRegion:t.monorailRegion,resourceTimingSamplingRate:Number(t.resourceTimingSamplingRate)||void 0,spaMode:"true"===t.spaMode,shs:"true"===t.shs}}(t),jt=new zt(e),e.spaMode&&(window.PerfKit={navigate:()=>{Ot?Ot=!1:Ft||(Ut(),jt=new zt(e),Ft=!0)},setPageType:t=>{e.storefrontData.pageType=t}}),e.shs&&Bt({debug:!0})}catch(t){console.error("🚫 Error initializing PerfKit:",t.message)}var e}()}();
